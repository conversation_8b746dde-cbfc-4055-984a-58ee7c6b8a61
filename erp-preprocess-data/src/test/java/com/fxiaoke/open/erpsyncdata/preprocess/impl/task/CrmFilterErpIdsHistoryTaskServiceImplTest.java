package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CrmFilterErpIdsHistoryTaskServiceImpl 单元测试类
 * 
 * 测试原则：
 * 1. 不依赖真实的外部服务调用
 * 2. 使用 Mock 对象模拟所有外部依赖
 * 3. 只测试当前类的业务逻辑
 * 4. 测试用例要独立且可重复执行
 * 5. 测试各个依赖服务的交互，而不是调用真实的业务方法
 * 6. 使用 Given-When-Then 结构组织测试
 * 7. 测试边界条件和异常场景
 *
 * 注意：由于缺少一些依赖类（如 IdFieldKeyManager, ErpIdArg 等），
 * 本测试文件只包含基本的可测试方法。
 *
 * <AUTHOR> Assistant
 * @date 2025/06/25
 */
@ExtendWith(MockitoExtension.class)
class CrmFilterErpIdsHistoryTaskServiceImplTest {

    @Mock
    private PollingDataSpeedRateLimitManager pollingDataSpeedRateLimitManager;

    @Mock
    private SyncLogManager syncLogManager;

    @Mock
    private SyncDataMappingManager syncDataMappingManager;

    @Mock
    private ErpDataService erpDataService;

    @Mock
    private ErpConnectInfoManager erpConnectInfoManager;

    @Mock
    private ErpFieldManager erpFieldManager;

    @Mock
    private ErpTempDataDao erpTempDataDao;

    @InjectMocks
    private CrmFilterErpIdsHistoryTaskServiceImpl crmFilterErpIdsHistoryTaskService;

    // 测试常量
    private static final String TEST_TENANT_ID = "test_tenant_123";
    private static final String TEST_DC_ID = "test_dc_456";
    private static final String TEST_OBJ_API_NAME = "test_obj";
    private static final String TEST_REAL_OBJ_API_NAME = "real_test_obj";
    private static final String TEST_TASK_ID = "task_123";
    private static final String TEST_LOG_ID = "log_123";

    @BeforeEach
    void setUp() {
        // 初始化测试环境
        reset(pollingDataSpeedRateLimitManager, syncLogManager, syncDataMappingManager,
                erpDataService, erpConnectInfoManager, erpFieldManager, erpTempDataDao);
    }

    /**
     * 测试 taskType 方法
     * Given: 调用 taskType 方法
     * When: 执行方法
     * Then: 返回正确的任务类型
     */
    @Test
    void testTaskType() {
        // When
        ErpHistoryDataTaskTypeEnum result = crmFilterErpIdsHistoryTaskService.taskType();

        // Then
        assertEquals(ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER, result);
    }

    /**
     * 测试 saveTaskSuccess 方法 - 成功场景
     * Given: 有效的任务数据和实体
     * When: 调用 saveTaskSuccess 方法
     * Then: 设置CRM过滤器并创建历史任务
     */
    @Test
    void testSaveTaskSuccess_Success() {
        // Given
        ErpHistoryDataTaskResult task = createTestTaskResult();
        ErpHistoryDataTaskEntity entity = createTestEntity();
        
        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when(spyService).setCrmsFilter(any(), any());
        doReturn(true).when(spyService).createHistoryTask(anyString(), anyList());

        // When
        boolean result = spyService.saveTaskSuccess(TEST_TENANT_ID, task, entity);

        // Then
        assertTrue(result);
        verify(spyService).setCrmsFilter(entity, task.getCrmFilters());
        verify(spyService).createHistoryTask(eq(TEST_TENANT_ID), eq(Lists.newArrayList(entity)));
    }

    /**
     * 测试 editTaskSuccess 方法 - 成功场景
     * Given: 有效的任务数据和实体
     * When: 调用 editTaskSuccess 方法
     * Then: 设置CRM过滤器并编辑历史快照任务
     */
    @Test
    void testEditTaskSuccess_Success() {
        // Given
        ErpHistoryDataTaskResult task = createTestTaskResult();
        ErpHistoryDataTaskEntity entity = createTestEntity();
        
        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when(spyService).setCrmsFilter(any(), any());
        doReturn(true).when(spyService).editHistorySnapshotTask(anyString(), any());

        // When
        boolean result = spyService.editTaskSuccess(TEST_TENANT_ID, task, entity);

        // Then
        assertTrue(result);
        verify(spyService).setCrmsFilter(entity, task.getCrmFilters());
        verify(spyService).editHistorySnapshotTask(TEST_TENANT_ID, entity);
    }

    /**
     * 测试 afterConvert2Vo 方法
     * Given: 任务结果和实体
     * When: 调用 afterConvert2Vo 方法
     * Then: 调用父类方法并清空 dataIds
     */
    @Test
    void testAfterConvert2Vo() {
        // Given
        ErpHistoryDataTaskResult copy = createTestTaskResult();
        ErpHistoryDataTaskEntity entity = createTestEntity();
        copy.setDataIds(Arrays.asList("id1", "id2", "id3"));
        
        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when((FilterCrmHistoryTaskServiceImpl) spyService).afterConvert2Vo(any(), any());

        // When
        spyService.afterConvert2Vo(copy, entity);

        // Then
        assertNull(copy.getDataIds());
        verify((FilterCrmHistoryTaskServiceImpl) spyService).afterConvert2Vo(copy, entity);
    }

    /**
     * 测试基本的对象创建和依赖注入
     * Given: 测试环境已设置
     * When: 验证对象状态
     * Then: 所有对象都应正确创建
     */
    @Test
    void testBasicObjectCreation() {
        // Given & When & Then
        assertNotNull(crmFilterErpIdsHistoryTaskService);
        assertNotNull(pollingDataSpeedRateLimitManager);
        assertNotNull(syncLogManager);
        assertNotNull(syncDataMappingManager);
        assertNotNull(erpDataService);
        assertNotNull(erpConnectInfoManager);
        assertNotNull(erpFieldManager);
        assertNotNull(erpTempDataDao);
    }

    /**
     * 测试空参数处理
     * Given: 传入 null 参数
     * When: 调用相关方法
     * Then: 正确处理空参数而不抛出异常
     */
    @Test
    void testNullParameterHandling() {
        // Given
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when((FilterCrmHistoryTaskServiceImpl) spyService).afterConvert2Vo(any(), any());

        // When & Then - 测试 afterConvert2Vo 的空参数处理
        assertDoesNotThrow(() -> spyService.afterConvert2Vo(null, createTestEntity()));
        assertDoesNotThrow(() -> spyService.afterConvert2Vo(createTestTaskResult(), null));
        assertDoesNotThrow(() -> spyService.afterConvert2Vo(null, null));
    }

    /**
     * 创建测试用的 ErpHistoryDataTaskResult
     */
    private ErpHistoryDataTaskResult createTestTaskResult() {
        ErpHistoryDataTaskResult task = new ErpHistoryDataTaskResult();
        task.setObjApiName(TEST_OBJ_API_NAME);
        task.setTaskName("Test Task");
        task.setRemark("Test Remark");
        task.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER.getStatus());
        task.setExecuteTime(System.currentTimeMillis() + 3600000L);
        task.setPriority(1);
        
        // 创建 CRM 过滤器
        ErpHistoryDataTaskResult.CrmFilters crmFilter = new ErpHistoryDataTaskResult.CrmFilters();
        crmFilter.setObjectApiName("crm_obj");
        task.setCrmFilters(Arrays.asList(crmFilter));
        
        return task;
    }

    /**
     * 创建测试用的 ErpHistoryDataTaskEntity
     */
    private ErpHistoryDataTaskEntity createTestEntity() {
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();
        entity.setId(TEST_TASK_ID);
        entity.setTenantId(TEST_TENANT_ID);
        entity.setDataCenterId(TEST_DC_ID);
        entity.setTaskName("Test Task");
        entity.setObjApiName(TEST_OBJ_API_NAME);
        entity.setRealObjApiName(TEST_REAL_OBJ_API_NAME);
        entity.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER.getStatus());
        entity.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_WAITING.getStatus());
        entity.setTaskNum("TASK_" + System.currentTimeMillis());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setExecuteTime(System.currentTimeMillis() + 3600000);
        return entity;
    }

    /**
     * 创建测试用的 ErpObjectFieldEntity
     */
    private ErpObjectFieldEntity createTestIdField() {
        ErpObjectFieldEntity idField = new ErpObjectFieldEntity();
        idField.setId("field_123");
        idField.setTenantId(TEST_TENANT_ID);
        idField.setErpObjectApiName(TEST_OBJ_API_NAME);
        idField.setFieldApiName("id");
        idField.setFieldLabel("ID");
        idField.setFieldExtendValue("{}"); // 空的复合ID配置
        return idField;
    }
}
